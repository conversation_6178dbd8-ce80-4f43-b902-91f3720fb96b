import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/user_status_service.dart';
import '../admin/models/user_management_model.dart';

/// Middleware to check user status and block access if user is blocked
class UserStatusMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // Skip status check for certain routes
    final skipRoutes = [
      '/login',
      '/signup',
      '/blocked-user',
      '/admin-login',
      '/admin',
      '/landing',
      '/forgot-password',
      '/verify-email',
    ];

    if (route != null &&
        skipRoutes.any((skipRoute) => route.startsWith(skipRoute))) {
      return null; // Allow access to these routes
    }

    // Check if user is authenticated
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return null; // Let auth middleware handle this
    }

    // Only check if UserStatusService is already registered
    // Don't create it here as it should be created after authentication
    if (Get.isRegistered<UserStatusService>()) {
      final userStatusService = Get.find<UserStatusService>();

      // Check if user is blocked
      if (userStatusService.isBlocked) {
        return const RouteSettings(name: '/blocked-user');
      }
    }

    return null; // Allow access
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    // Additional check when page is called
    _checkUserStatusAsync();
    return page;
  }

  /// Asynchronously check user status and redirect if blocked
  void _checkUserStatusAsync() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // Only proceed if UserStatusService is already registered
      if (!Get.isRegistered<UserStatusService>()) {
        return; // Service not ready yet
      }

      final userStatusService = Get.find<UserStatusService>();

      // Check current status
      final status = await userStatusService.checkUserStatus();

      if (status == UserStatus.blocked && Get.currentRoute != '/blocked-user') {
        // User is blocked, redirect to blocked screen
        Get.offAllNamed('/blocked-user');
      }
    } catch (e) {
      // Don't block navigation on error, just log it
      debugPrint('UserStatusMiddleware: Error checking user status: $e');
    }
  }
}

/// Guard function to check user status before allowing access to sensitive operations
class UserStatusGuard {
  static Future<bool> checkAccess() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      // Only proceed if UserStatusService is already registered
      if (!Get.isRegistered<UserStatusService>()) {
        return false; // Service not ready yet, allow access
      }

      final userStatusService = Get.find<UserStatusService>();
      final status = await userStatusService.checkUserStatus();

      if (status == UserStatus.blocked) {
        // Show blocked message and redirect
        Get.snackbar(
          'Access Denied',
          'Your account has been suspended',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        Get.offAllNamed('/blocked-user');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('UserStatusGuard: Error checking access: $e');
      return true; // Allow access on error to prevent app breaking
    }
  }

  /// Quick synchronous check using cached status
  static bool quickCheck() {
    try {
      if (!Get.isRegistered<UserStatusService>()) {
        return true; // Allow if service not initialized
      }

      final userStatusService = Get.find<UserStatusService>();
      return !userStatusService.isBlocked;
    } catch (e) {
      debugPrint('UserStatusGuard: Error in quick check: $e');
      return true; // Allow access on error
    }
  }

  /// Show blocked user dialog
  static void showBlockedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.block, color: Colors.red),
            SizedBox(width: 8),
            Text('Account Suspended'),
          ],
        ),
        content: const Text(
          'Your account has been suspended. Please contact support for assistance.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              Get.offAllNamed('/blocked-user');
            },
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }
}
