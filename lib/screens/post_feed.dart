import 'dart:async';
import 'package:flutter/material.dart';
import 'package:money_mouthy_two/screens/categories_ranking.dart';
import '../services/post_service.dart';

class PostFeedScreen extends StatefulWidget {
  const PostFeedScreen({super.key});

  @override
  State<PostFeedScreen> createState() => _PostFeedScreenState();
}

class _PostFeedScreenState extends State<PostFeedScreen> {
  final PostService _postService = PostService();
  String _selectedCategory = 'All';
  String _sortBy = 'Highest Paid';
  List<Post> _posts = [];
  bool _isLoading = true;
  StreamSubscription<List<Post>>? _postsSubscription;

  @override
  void initState() {
    super.initState();
    _initializeAndLoadPosts();
  }

  @override
  void dispose() {
    _postsSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeAndLoadPosts() async {
    setState(() => _isLoading = true);

    try {
      // Ensure PostService is initialized
      if (!_postService.isInitialized) {
        await _postService.initialize();
      }

      // Listen to the real-time posts stream
      _postsSubscription?.cancel();
      _postsSubscription = _postService
          .getPostsStreamSortedBy(_sortBy, category: _selectedCategory)
          .listen(
        (posts) {
          if (mounted) {
            setState(() {
              _posts = posts;
              _isLoading = false;
            });
          }
        },
        onError: (error) {
          debugPrint('Error loading posts: $error');
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        },
      );
    } catch (e) {
      debugPrint('Error initializing posts: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshPosts() async {
    try {
      await _postService.refreshPosts();
      // The stream will automatically update the UI
    } catch (e) {
      debugPrint('Error refreshing posts: $e');
    }
  }

  void _updatePostsFilter() {
    // Cancel current subscription and create new one with updated filters
    _postsSubscription?.cancel();
    _postsSubscription = _postService
        .getPostsStreamSortedBy(_sortBy, category: _selectedCategory)
        .listen(
      (posts) {
        if (mounted) {
          setState(() {
            _posts = posts;
          });
        }
      },
    );
  }

  void _purchasePost(String postId, double price) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post purchased for \$${price.toStringAsFixed(2)}!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5159FF) : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
          border: isSelected ? null : Border.all(color: Colors.grey[300]!),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildPostCard(Post post) {
    final isPaidPost = post.price > 0;
    final hasAccess = post.isPaid || !isPaidPost;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF5159FF),
                child: Text(
                  post.author.split(' ').map((e) => e[0]).take(2).join(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.author,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '${post.timeAgo} • ${post.category}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 11),
                    ),
                  ],
                ),
              ),
              if (isPaidPost)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: hasAccess
                        ? Colors.green.withOpacity(0.1)
                        : const Color(0xFF5159FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: hasAccess ? Colors.green : const Color(0xFF5159FF),
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    hasAccess ? 'Owned' : post.formattedPrice,
                    style: TextStyle(
                      color: hasAccess ? Colors.green : const Color(0xFF5159FF),
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
            ],
          ),

          // Title
          if (post.title != null && post.title!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              post.title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          const SizedBox(height: 6),

          // Content
          Text(
            hasAccess
                ? post.content
                : '${post.content.substring(0, post.content.length > 120 ? 120 : post.content.length)}${post.content.length > 120 ? '...' : ''}',
            style: TextStyle(
              fontSize: 13,
              color: hasAccess ? Colors.black87 : Colors.grey[700],
              height: 1.4,
            ),
            maxLines: hasAccess ? 4 : 3,
            overflow: TextOverflow.ellipsis,
          ),

          // Premium overlay
          if (isPaidPost && !hasAccess) ...[
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF5159FF).withOpacity(0.03),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: const Color(0xFF5159FF).withOpacity(0.15),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lock_outline,
                    color: const Color(0xFF5159FF),
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Premium Content',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF5159FF),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          'Purchase to read full content',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () => _purchasePost(post.id, post.price),
                    style: TextButton.styleFrom(
                      backgroundColor: const Color(0xFF5159FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Buy ${post.formattedPrice}',
                      style: const TextStyle(fontSize: 11),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 8),

          // Actions
          Row(
            children: [
              _buildActionButton(Icons.favorite_border, '${post.likes}', () {}),
              const SizedBox(width: 16),
              _buildActionButton(
                Icons.visibility_outlined,
                '${post.views}',
                () {},
              ),
              const Spacer(),
              InkWell(
                onTap: () {},
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(6),
                  child: Icon(
                    Icons.share_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 3),
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final categories = _postService.getCategories();
    final sortOptions = _postService.getSortOptions();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              floating: true,
              snap: true,
              backgroundColor: Colors.white,
              elevation: 0.5,
              title: const Text(
                'Money Mouthy',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              actions: [
                IconButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CategoriesRankingScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.leaderboard, color: Colors.black),
                ),
                IconButton(
                  onPressed: () async {
                    // Navigate to home screen and switch to create post tab
                    Navigator.of(context).pushReplacementNamed('/home');
                    // Note: The MainNavigationScreen should handle switching to create post tab
                  },
                  icon: const Icon(Icons.add_circle, color: Color(0xFF5159FF)),
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // Compact filter row
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
                      child: Row(
                        children: [
                          // Category dropdown
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedCategory,
                                isDense: true,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                                items: categories.map((category) {
                                  return DropdownMenuItem(
                                    value: category,
                                    child: Text(category),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedCategory = value;
                                    });
                                    _updatePostsFilter();
                                  }
                                },
                              ),
                            ),
                          ),

                          const SizedBox(width: 12),

                          // Sort dropdown
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _sortBy,
                                isDense: true,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                                items: sortOptions.map((option) {
                                  return DropdownMenuItem(
                                    value: option,
                                    child: Text(option),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _sortBy = value;
                                    });
                                    _updatePostsFilter();
                                  }
                                },
                              ),
                            ),
                          ),

                          const Spacer(),

                          // Posts count
                          Text(
                            '${_posts.length} posts',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Thin separator
                    Container(height: 1, color: Colors.grey[200]),
                  ],
                ),
              ),
            ),
          ];
        },
        body: RefreshIndicator(
          onRefresh: _refreshPosts,
          child: _isLoading
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(50),
                    child: CircularProgressIndicator(),
                  ),
                )
              : _posts.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.article_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No posts found',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Be the first to create a post!',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () async {
                              final result = await Navigator.pushNamed(
                                context,
                                '/create_post',
                              );
                              if (result != null &&
                                  result is Map &&
                                  result['success'] == true) {
                                _refreshPosts();
                              }
                            },
                            icon: const Icon(Icons.add),
                            label: const Text('Create First Post'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF5159FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.fromLTRB(12, 8, 12, 80),
                      itemCount: _posts.length,
                      itemBuilder: (context, index) {
                        return _buildPostCard(_posts[index]);
                      },
                    ),
        ),
      ),
    );
  }
}
