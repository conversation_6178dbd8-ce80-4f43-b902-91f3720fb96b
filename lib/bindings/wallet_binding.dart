import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/category_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/draft_controller.dart';
import '../controllers/poll_controller.dart';
import '../services/user_status_service.dart';

class WalletBinding extends Bindings {
  @override
  void dependencies() {
    // Register WalletController as a singleton
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Register all core controllers that should be available app-wide
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class CoreBinding extends Bindings {
  @override
  void dependencies() {
    debugPrint('CoreBinding: Initializing core services...');

    // Register only non-authentication dependent controllers and essential services
    Get.put<DraftController>(DraftController(),
        permanent: true); // Uses local storage only

    // UserStatusService needs to be available early for route protection
    Get.put<UserStatusService>(UserStatusService(), permanent: true);

    debugPrint('CoreBinding: Core services initialized successfully');
  }
}

class AuthenticatedBinding extends Bindings {
  @override
  void dependencies() {
    debugPrint(
        'AuthenticatedBinding: Initializing authenticated controllers...');

    // Only register if not already registered to prevent duplicates
    if (!Get.isRegistered<CategoryController>()) {
      Get.put<CategoryController>(CategoryController(), permanent: true);
      debugPrint('AuthenticatedBinding: CategoryController registered');
    }

    if (!Get.isRegistered<WalletController>()) {
      Get.put<WalletController>(WalletController(), permanent: true);
      debugPrint('AuthenticatedBinding: WalletController registered');
    }

    if (!Get.isRegistered<PostController>()) {
      Get.put<PostController>(PostController(), permanent: true);
      debugPrint('AuthenticatedBinding: PostController registered');
    }

    if (!Get.isRegistered<PollController>()) {
      Get.put<PollController>(PollController(), permanent: true);
      debugPrint('AuthenticatedBinding: PollController registered');
    }

    debugPrint(
        'AuthenticatedBinding: All authenticated controllers initialized');
  }
}
