import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../admin/models/user_management_model.dart';

/// Service to check and monitor user status for blocking functionality
class UserStatusService extends GetxService {
  static UserStatusService get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Reactive variables
  final _currentUserStatus = UserStatus.active.obs;
  final _isBlocked = false.obs;
  final _blockReason = ''.obs;
  final _isLoading = false.obs;

  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  // Getters
  UserStatus get currentUserStatus => _currentUserStatus.value;
  bool get isBlocked => _isBlocked.value;
  String get blockReason => _blockReason.value;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    // Don't auto-initialize - wait for explicit initialization after authentication
    debugPrint(
        'UserStatusService: Created but not initialized - waiting for authentication');
  }

  /// Initialize status monitoring after authentication
  void initializeWithAuth() {
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint(
          'UserStatusService: User not authenticated, skipping initialization');
      return;
    }

    debugPrint(
        'UserStatusService: Initializing for authenticated user: ${user.uid}');
    _initializeStatusMonitoring();
  }

  @override
  void onClose() {
    _statusSubscription?.cancel();
    super.onClose();
  }

  /// Initialize real-time status monitoring for current user
  void _initializeStatusMonitoring() {
    final user = _auth.currentUser;
    if (user == null) return;

    _statusSubscription =
        _firestore.collection('users').doc(user.uid).snapshots().listen(
      (snapshot) {
        if (snapshot.exists) {
          final userData = snapshot.data()!;
          _updateUserStatus(userData);
        }
      },
      onError: (error) {
        debugPrint('UserStatusService: Error monitoring user status: $error');
      },
    );
  }

  /// Update user status from Firestore data
  void _updateUserStatus(Map<String, dynamic> userData) {
    final statusString = userData['status'] as String?;
    final status = UserStatus.values.firstWhere(
      (s) => s.name == statusString,
      orElse: () => UserStatus.active,
    );

    _currentUserStatus.value = status;
    _isBlocked.value = status == UserStatus.blocked;
    _blockReason.value = userData['blockReason'] as String? ?? '';

    debugPrint(
        'UserStatusService: User status updated - Status: ${status.name}, Blocked: ${_isBlocked.value}');

    // If user is blocked, trigger blocking flow
    if (_isBlocked.value) {
      _handleUserBlocked();
    }
  }

  /// Handle when user is blocked
  void _handleUserBlocked() {
    debugPrint(
        'UserStatusService: User is blocked, redirecting to support page');

    // Navigate to blocked user screen
    Get.offAllNamed('/blocked-user');
  }

  /// Check user status manually (for one-time checks)
  Future<UserStatus> checkUserStatus([String? userId]) async {
    try {
      _isLoading.value = true;

      final targetUserId = userId ?? _auth.currentUser?.uid;
      if (targetUserId == null) {
        return UserStatus.active;
      }

      final userDoc =
          await _firestore.collection('users').doc(targetUserId).get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final statusString = userData['status'] as String?;
        final status = UserStatus.values.firstWhere(
          (s) => s.name == statusString,
          orElse: () => UserStatus.active,
        );

        // Update current user status if checking own status
        if (targetUserId == _auth.currentUser?.uid) {
          _updateUserStatus(userData);
        }

        return status;
      }

      return UserStatus.active;
    } catch (e) {
      debugPrint('UserStatusService: Error checking user status: $e');
      return UserStatus.active;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Check if user is blocked (returns true if blocked)
  Future<bool> isUserBlocked([String? userId]) async {
    final status = await checkUserStatus(userId);
    return status == UserStatus.blocked;
  }

  /// Get block reason for a user
  Future<String?> getBlockReason([String? userId]) async {
    try {
      final targetUserId = userId ?? _auth.currentUser?.uid;
      if (targetUserId == null) return null;

      final userDoc =
          await _firestore.collection('users').doc(targetUserId).get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        return userData['blockReason'] as String?;
      }

      return null;
    } catch (e) {
      debugPrint('UserStatusService: Error getting block reason: $e');
      return null;
    }
  }

  /// Force refresh user status
  Future<void> refreshUserStatus() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _updateUserStatus(userData);
      }
    } catch (e) {
      debugPrint('UserStatusService: Error refreshing user status: $e');
    }
  }

  /// Initialize status monitoring for a specific user (for admin use)
  void startMonitoringUser(String userId) {
    _statusSubscription?.cancel();

    _statusSubscription =
        _firestore.collection('users').doc(userId).snapshots().listen(
      (snapshot) {
        if (snapshot.exists) {
          final userData = snapshot.data()!;
          _updateUserStatus(userData);
        }
      },
      onError: (error) {
        debugPrint('UserStatusService: Error monitoring user $userId: $error');
      },
    );
  }

  /// Stop monitoring user status
  void stopMonitoring() {
    _statusSubscription?.cancel();
    _statusSubscription = null;
  }
}
