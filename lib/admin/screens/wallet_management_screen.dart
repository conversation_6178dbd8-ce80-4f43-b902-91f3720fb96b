import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/wallet_management_controller.dart';
import '../models/wallet_management_model.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/admin_bottom_sheet.dart';
import '../widgets/wallet_status_chip.dart';
import '../widgets/wallet_details_dialog.dart';

class WalletManagementScreen extends StatefulWidget {
  const WalletManagementScreen({super.key});

  @override
  State<WalletManagementScreen> createState() => _WalletManagementScreenState();
}

class _WalletManagementScreenState extends State<WalletManagementScreen> {
  final _walletController = Get.find<WalletManagementController>();
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'Wallet Management',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header and Stats
          _buildHeaderSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Filters and Search
          _buildFiltersSection(),
          const SizedBox(height: AdminTheme.spacingLarge),

          // Wallets Table
          Expanded(child: _buildWalletsTable()),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Wallets',
                    _walletController.totalWallets.toString(),
                    Icons.account_balance_wallet,
                    AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Total Balance',
                    '\$${_walletController.totalBalance.toStringAsFixed(2)}',
                    Icons.attach_money,
                    AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingSmall),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Active Wallets',
                    _walletController.totalWallets.toString(),
                    Icons.account_balance,
                    AdminTheme.warningColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'Transactions Today',
                    _calculateTransactionsToday().toString(),
                    Icons.swap_horiz,
                    AdminTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Wallets',
                _walletController.totalWallets.toString(),
                Icons.account_balance_wallet,
                AdminTheme.primaryColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Total Balance',
                '\$${_walletController.totalBalance.toStringAsFixed(2)}',
                Icons.attach_money,
                AdminTheme.successColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Active Wallets',
                _walletController.totalWallets.toString(),
                Icons.account_balance,
                AdminTheme.warningColor,
              ),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: _buildStatCard(
                'Transactions Today',
                _calculateTransactionsToday().toString(),
                Icons.swap_horiz,
                AdminTheme.errorColor,
              ),
            ),
          ],
        );
      }
    });
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isMobile ? 20 : 24,
                ),
              ),
              const Spacer(),
              if (!isMobile)
                Icon(
                  Icons.trending_up,
                  color: AdminTheme.textSecondary,
                  size: 16,
                ),
            ],
          ),
          SizedBox(
              height: isMobile
                  ? AdminTheme.spacingSmall
                  : AdminTheme.spacingMedium),
          Text(
            value,
            style: AdminTheme.headingMedium.copyWith(
              fontSize: isMobile ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: AdminTheme.textPrimary,
            ),
          ),
          const SizedBox(height: AdminTheme.spacingXSmall),
          Text(
            title,
            style: AdminTheme.bodySmall.copyWith(
              color: AdminTheme.textSecondary,
              fontSize: isMobile ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      padding: EdgeInsets.all(
          isMobile ? AdminTheme.spacingMedium : AdminTheme.spacingLarge),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: isMobile ? _buildMobileFilters() : _buildDesktopFilters(),
    );
  }

  Widget _buildMobileFilters() {
    return Column(
      children: [
        // Search Field
        TextField(
          controller: _searchController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Search wallets...',
            hintText: 'User ID, name, username, or email',
            prefixIcon: const Icon(Icons.search),
          ),
          onChanged: _walletController.setSearchQuery,
        ),
        const SizedBox(height: AdminTheme.spacingSmall),

        // Status Filter and Clear Button Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Obx(() => DropdownButtonFormField<WalletStatus?>(
                    value: _walletController.selectedStatus,
                    decoration: AdminTheme.inputDecoration(
                      labelText: 'Status',
                      prefixIcon: const Icon(Icons.filter_list),
                    ),
                    items: [
                      const DropdownMenuItem<WalletStatus?>(
                        value: null,
                        child: Text('All'),
                      ),
                      ...WalletStatus.values.map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.name.toUpperCase()),
                          )),
                    ],
                    onChanged: _walletController.setStatusFilter,
                  )),
            ),
            const SizedBox(width: AdminTheme.spacingMedium),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _walletController.clearFilters();
                },
                icon: const Icon(Icons.clear, size: 18),
                label: const Text('Clear'),
                style: AdminTheme.secondaryButtonStyle,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopFilters() {
    return Row(
      children: [
        // Search Field
        Expanded(
          flex: 2,
          child: TextField(
            controller: _searchController,
            decoration: AdminTheme.inputDecoration(
              labelText: 'Search wallets...',
              hintText: 'Search by user ID, name, username, or email',
              prefixIcon: const Icon(Icons.search),
            ),
            onChanged: _walletController.setSearchQuery,
          ),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Status Filter
        Expanded(
          child: Obx(() => DropdownButtonFormField<WalletStatus?>(
                value: _walletController.selectedStatus,
                decoration: AdminTheme.inputDecoration(
                  labelText: 'Filter by Status',
                  prefixIcon: const Icon(Icons.filter_list),
                ),
                items: [
                  const DropdownMenuItem<WalletStatus?>(
                    value: null,
                    child: Text('All Statuses'),
                  ),
                  ...WalletStatus.values.map((status) => DropdownMenuItem(
                        value: status,
                        child: Text(status.name.toUpperCase()),
                      )),
                ],
                onChanged: _walletController.setStatusFilter,
              )),
        ),
        const SizedBox(width: AdminTheme.spacingMedium),

        // Clear Filters Button
        ElevatedButton.icon(
          onPressed: () {
            _searchController.clear();
            _walletController.clearFilters();
          },
          icon: const Icon(Icons.clear),
          label: const Text('Clear'),
          style: AdminTheme.secondaryButtonStyle,
        ),
      ],
    );
  }

  Widget _buildWalletsTable() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Container(
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        children: [
          // Table Header (only for desktop)
          if (!isMobile)
            Container(
              padding: const EdgeInsets.all(AdminTheme.spacingMedium),
              decoration: BoxDecoration(
                color: AdminTheme.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AdminTheme.radiusMedium),
                  topRight: Radius.circular(AdminTheme.radiusMedium),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'User',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Balance',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Status',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Last Updated',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Transactions',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 100,
                    child: Text(
                      'Actions',
                      style: AdminTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Table Body
          Expanded(
            child: Obx(() {
              if (_walletController.isLoading) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: AdminTheme.primaryColor,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'Loading wallets...',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (_walletController.wallets.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_balance_wallet_outlined,
                        size: 64,
                        color: AdminTheme.textSecondary,
                      ),
                      const SizedBox(height: AdminTheme.spacingMedium),
                      Text(
                        'No wallets found',
                        style: AdminTheme.headingSmall.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: AdminTheme.spacingSmall),
                      Text(
                        'Try adjusting your search or filters',
                        style: AdminTheme.bodyMedium.copyWith(
                          color: AdminTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: EdgeInsets.all(isMobile ? AdminTheme.spacingSmall : 0),
                itemCount: _walletController.wallets.length,
                itemBuilder: (context, index) {
                  final wallet = _walletController.wallets[index];
                  return isMobile
                      ? _buildMobileWalletCard(wallet)
                      : _buildDesktopWalletRow(wallet);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileWalletCard(WalletManagementModel wallet) {
    return Container(
      margin: const EdgeInsets.only(bottom: AdminTheme.spacingMedium),
      padding: const EdgeInsets.all(AdminTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
        boxShadow: AdminTheme.cardShadow,
        border: Border.all(color: AdminTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Wallet Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AdminTheme.spacingSmall),
                decoration: BoxDecoration(
                  color: AdminTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  color: AdminTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _walletController.getUserDisplayName(wallet.userId),
                      style: AdminTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AdminTheme.textPrimary,
                      ),
                    ),
                    Text(
                      wallet.userId,
                      style: AdminTheme.bodySmall.copyWith(
                        color: AdminTheme.textSecondary,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              WalletStatusChip(status: wallet.status),
            ],
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // Balance Section
          Container(
            padding: const EdgeInsets.all(AdminTheme.spacingSmall),
            decoration: BoxDecoration(
              color: AdminTheme.successColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AdminTheme.radiusSmall),
              border:
                  Border.all(color: AdminTheme.successColor.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance,
                  color: AdminTheme.successColor,
                  size: 20,
                ),
                const SizedBox(width: AdminTheme.spacingSmall),
                Text(
                  'Balance: ',
                  style: AdminTheme.bodyMedium.copyWith(
                    color: AdminTheme.successColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '\$${wallet.balance.toStringAsFixed(2)}',
                  style: AdminTheme.headingSmall.copyWith(
                    color: AdminTheme.successColor,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // Wallet Details
          Row(
            children: [
              Expanded(
                child: _buildWalletInfoItem(
                  'Last Updated',
                  _formatDate(wallet.lastUpdated),
                  Icons.access_time,
                ),
              ),
              Expanded(
                child: _buildWalletInfoItem(
                  'Transactions',
                  wallet.transactionCount.toString(),
                  Icons.swap_horiz,
                ),
              ),
            ],
          ),

          const SizedBox(height: AdminTheme.spacingSmall),

          // Actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showWalletDetails(wallet),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('View'),
                  style: AdminTheme.secondaryButtonStyle,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _handleWalletAction('add_funds', wallet),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add'),
                  style: AdminTheme.successButtonStyle,
                ),
              ),
              const SizedBox(width: AdminTheme.spacingSmall),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _handleWalletAction('deduct_funds', wallet),
                  icon: const Icon(Icons.remove, size: 16),
                  label: const Text('Deduct'),
                  style: AdminTheme.errorButtonStyle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWalletInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AdminTheme.textSecondary,
        ),
        const SizedBox(width: AdminTheme.spacingSmall),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textSecondary,
                  fontSize: 11,
                ),
              ),
              Text(
                value,
                style: AdminTheme.bodySmall.copyWith(
                  color: AdminTheme.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopWalletRow(WalletManagementModel wallet) {
    return Container(
      padding: const EdgeInsets.all(AdminTheme.spacingMedium),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AdminTheme.borderColor),
        ),
      ),
      child: Row(
        children: [
          // User Info
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _walletController.getUserDisplayName(wallet.userId),
                  style: AdminTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AdminTheme.textPrimary,
                  ),
                ),
                Text(
                  wallet.userId,
                  style: AdminTheme.bodySmall.copyWith(
                    fontFamily: 'monospace',
                    color: AdminTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Balance
          Expanded(
            child: Text(
              '\$${wallet.balance.toStringAsFixed(2)}',
              style: AdminTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: wallet.balance > 0
                    ? AdminTheme.successColor
                    : AdminTheme.textSecondary,
              ),
            ),
          ),

          // Status
          Expanded(
            child: WalletStatusChip(status: wallet.status),
          ),

          // Last Updated
          Expanded(
            child: Text(
              _formatDate(wallet.lastUpdated),
              style: AdminTheme.bodySmall.copyWith(
                color: AdminTheme.textPrimary,
              ),
            ),
          ),

          // Transaction Count
          Expanded(
            child: Text(
              wallet.transactionCount.toString(),
              style: AdminTheme.bodySmall.copyWith(
                color: AdminTheme.textPrimary,
              ),
            ),
          ),

          // Actions
          SizedBox(
            width: 100,
            child: Row(
              children: [
                IconButton(
                  icon: Icon(
                    Icons.visibility,
                    size: 18,
                    color: AdminTheme.primaryColor,
                  ),
                  onPressed: () => _showWalletDetails(wallet),
                  tooltip: 'View Details',
                ),
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    size: 18,
                    color: AdminTheme.textSecondary,
                  ),
                  onSelected: (value) => _handleWalletAction(value, wallet),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'adjust',
                      child: ListTile(
                        leading: Icon(Icons.account_balance, size: 16),
                        title: Text('Adjust Balance'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'add_funds',
                      child: ListTile(
                        leading: Icon(Icons.add_circle,
                            size: 16, color: Colors.green),
                        title: Text('Add Funds'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'deduct_funds',
                      child: ListTile(
                        leading: Icon(Icons.remove_circle,
                            size: 16, color: Colors.red),
                        title: Text('Deduct Funds'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'transactions',
                      child: ListTile(
                        leading: Icon(Icons.list, size: 16),
                        title: Text('View Transactions'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showWalletDetails(WalletManagementModel wallet) {
    AdminBottomSheet.show(
      context: context,
      title:
          'Wallet Details: ${_walletController.getUserDisplayName(wallet.userId)}',
      child: WalletDetailsDialog(wallet: wallet),
      height: MediaQuery.of(context).size.height * 0.8,
    );
  }

  void _handleWalletAction(String action, WalletManagementModel wallet) {
    switch (action) {
      case 'adjust':
        _showAdjustBalanceDialog(wallet);
        break;
      case 'add_funds':
        _showAddFundsDialog(wallet);
        break;
      case 'deduct_funds':
        _showDeductFundsDialog(wallet);
        break;

      case 'transactions':
        _showTransactionsDialog(wallet);
        break;
    }
  }

  void _showAdjustBalanceDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Adjust Balance: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.primaryColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet,
                  color: AdminTheme.primaryColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Adjustment Amount',
            prefixIcon: const Icon(Icons.edit, color: AdminTheme.primaryColor),
            hintText: '0.00',
            helperText: 'Use negative values to deduct (e.g., -50.00)',
          ),
          keyboardType: const TextInputType.numberWithOptions(
              decimal: true, signed: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for adjustment',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this adjustment...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null && reasonController.text.trim().isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            amount,
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Wallet balance adjusted successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Adjust Balance',
    );
  }

  int _calculateTransactionsToday() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    return _walletController.transactions.where((transaction) {
      return transaction.timestamp.isAfter(startOfDay);
    }).length;
  }

  void _showAddFundsDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Add Funds: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.successColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.successColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet,
                  color: AdminTheme.successColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.successColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Amount to Add',
            prefixIcon:
                const Icon(Icons.add_circle, color: AdminTheme.successColor),
            hintText: '0.00',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for adding funds',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this transaction...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null &&
            amount > 0 &&
            reasonController.text.trim().isNotEmpty) {
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            amount,
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Funds added successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Add Funds',
    );
  }

  void _showDeductFundsDialog(WalletManagementModel wallet) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    FormBottomSheet.show(
      context: context,
      title:
          'Deduct Funds: ${_walletController.getUserDisplayName(wallet.userId)}',
      fields: [
        Container(
          padding: const EdgeInsets.all(AdminTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AdminTheme.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AdminTheme.radiusMedium),
            border: Border.all(color: AdminTheme.errorColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.account_balance_wallet, color: AdminTheme.errorColor),
              const SizedBox(width: AdminTheme.spacingMedium),
              Text(
                'Current Balance: \$${wallet.balance.toStringAsFixed(2)}',
                style: AdminTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AdminTheme.errorColor,
                ),
              ),
            ],
          ),
        ),
        TextField(
          controller: amountController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Amount to Deduct',
            prefixIcon:
                const Icon(Icons.remove_circle, color: AdminTheme.errorColor),
            hintText: '0.00',
            helperText: 'Maximum: \$${wallet.balance.toStringAsFixed(2)}',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        TextField(
          controller: reasonController,
          decoration: AdminTheme.inputDecoration(
            labelText: 'Reason for deducting funds',
            prefixIcon: const Icon(Icons.note_add),
            hintText: 'Enter reason for this transaction...',
          ),
          maxLines: 3,
        ),
      ],
      onSave: () async {
        final amount = double.tryParse(amountController.text);
        if (amount != null &&
            amount > 0 &&
            reasonController.text.trim().isNotEmpty) {
          if (amount > wallet.balance) {
            Get.snackbar(
              'Error',
              'Cannot deduct more than current balance',
              backgroundColor: AdminTheme.errorColor,
              colorText: AdminTheme.textOnPrimary,
            );
            return;
          }
          Navigator.of(context).pop();
          final success = await _walletController.adjustWalletBalance(
            wallet.userId,
            -amount, // Negative amount for deduction
            reasonController.text.trim(),
          );
          if (success) {
            Get.snackbar(
              'Success',
              'Funds deducted successfully',
              backgroundColor: AdminTheme.successColor,
              colorText: AdminTheme.textOnPrimary,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            'Please enter a valid amount and reason',
            backgroundColor: AdminTheme.errorColor,
            colorText: AdminTheme.textOnPrimary,
          );
        }
      },
      saveText: 'Deduct Funds',
    );
  }

  void _showTransactionsDialog(WalletManagementModel wallet) {
    Get.snackbar('Info',
        'Detailed transaction history is available in the wallet details dialog');
  }
}
